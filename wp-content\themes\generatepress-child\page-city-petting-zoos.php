<?php
/**
 * Template Name: City Petting Zoos Page
 * 
 * Custom template for city petting zoo pages
 */

get_header();

// Helper function for console logging
function console_log_city($message) {
    echo "<script>console.log('City Page: " . esc_js($message) . "');</script>";
    if (WP_DEBUG) {
        error_log("[City Page] " . $message);
    }
}

// Extract city and state from page title or URL
$page_title = get_the_title();
$city_name = '';
$state_name = '';

// First try to get from URL structure
$current_url = $_SERVER['REQUEST_URI'];
console_log_city("Current URL: " . $current_url);

// Updated regex to match city pages with state abbreviation: /florida/miami-fl/
if (preg_match('/^\/([^\/]+)\/([^\/]+-[a-z]{2})\/?$/', $current_url, $url_matches)) {
    $state_slug = $url_matches[1];
    $city_slug_with_state = $url_matches[2];

    $state_name = str_replace('-', ' ', $state_slug);
    $state_name = ucwords($state_name);

    // Extract city name from city-state format (e.g., miami-fl -> miami)
    if (preg_match('/^(.+)-[a-z]{2}$/', $city_slug_with_state, $city_matches)) {
        $city_name = str_replace('-', ' ', $city_matches[1]);
        $city_name = ucwords($city_name);
    } else {
        // Fallback if format doesn't match expected pattern
        $city_name = str_replace('-', ' ', $city_slug_with_state);
        $city_name = ucwords($city_name);
    }

    console_log_city("State name from URL: " . $state_name);
    console_log_city("City name from URL: " . $city_name);
    console_log_city("City slug with state: " . $city_slug_with_state);
}

// Fallback to page title extraction
if ((empty($city_name) || empty($state_name)) && preg_match('/Best Petting Zoos in (.+), (.+)/', $page_title, $matches)) {
    $city_name = trim($matches[1]);
    $state_name = trim($matches[2]);
    console_log_city("City/State from page title: " . $city_name . ", " . $state_name);
}

console_log_city("City Page Template Loaded for: " . $city_name . ", " . $state_name);
console_log_city("Page title: " . $page_title);

// Get city and state terms from location taxonomy
$city_term = null;
$state_term = null;

if ($city_name && $state_name) {
    // First find the state term
    $state_term = get_term_by('name', $state_name, 'location');
    if (!$state_term) {
        // Try variations
        $state_variations = array(
            $state_name,
            str_replace(' ', '', $state_name),
            ucwords(strtolower($state_name))
        );
        
        foreach ($state_variations as $variation) {
            $state_term = get_term_by('name', $variation, 'location');
            if ($state_term) break;
        }
    }
    
    // Then find the city term (could be child of state or separate)
    $city_term = get_term_by('name', $city_name, 'location');
    if (!$city_term) {
        // Try variations
        $city_variations = array(
            $city_name,
            str_replace(' ', '', $city_name),
            ucwords(strtolower($city_name))
        );
        
        foreach ($city_variations as $variation) {
            $city_term = get_term_by('name', $variation, 'location');
            if ($city_term) break;
        }
    }
}

console_log_city("State term found: " . ($state_term ? $state_term->name : 'Not found'));
console_log_city("City term found: " . ($city_term ? $city_term->name : 'Not found'));

// Query petting zoos in this city - prioritize city-specific results
$all_zoos = array();

// First, try to find zoos by address containing the city name (most accurate)
$address_args = array(
    'post_type' => 'petting_zoo',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'meta_query' => array(
        'relation' => 'AND',
        array(
            'key' => '_petting_zoo_address',
            'value' => $city_name,
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_petting_zoo_address',
            'value' => $state_name,
            'compare' => 'LIKE'
        )
    )
);

$zoos_by_address = get_posts($address_args);
console_log_city("Found " . count($zoos_by_address) . " zoos by address search");

// If we found zoos by address, use those primarily
if (!empty($zoos_by_address)) {
    $all_zoos = $zoos_by_address;
} else {
    // Fallback: try taxonomy-based search
    $zoo_args = array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    );

    // Build tax query for city and/or state
    $tax_query = array('relation' => 'OR');

    if ($city_term) {
        $tax_query[] = array(
            'taxonomy' => 'location',
            'field'    => 'term_id',
            'terms'    => $city_term->term_id,
        );
    }

    if ($state_term) {
        $tax_query[] = array(
            'taxonomy' => 'location',
            'field'    => 'term_id',
            'terms'    => $state_term->term_id,
        );
    }

    if (count($tax_query) > 1) {
        $zoo_args['tax_query'] = $tax_query;
    }

    $zoos_by_taxonomy = get_posts($zoo_args);
    console_log_city("Found " . count($zoos_by_taxonomy) . " zoos by taxonomy search");

    // Filter taxonomy results to only include those with city name in address
    $filtered_zoos = array();
    foreach ($zoos_by_taxonomy as $zoo) {
        $address = get_post_meta($zoo->ID, '_petting_zoo_address', true);
        if ($address && (stripos($address, $city_name) !== false)) {
            $filtered_zoos[] = $zoo;
        }
    }

    $all_zoos = !empty($filtered_zoos) ? $filtered_zoos : $zoos_by_taxonomy;
    console_log_city("After filtering by city name: " . count($all_zoos) . " zoos");
}

console_log_city("Found " . count($all_zoos) . " zoos in " . $city_name . ", " . $state_name);

// Get filter options
$animal_types = get_terms(array('taxonomy' => 'animal_type', 'hide_empty' => true));
$zoo_types = get_terms(array('taxonomy' => 'zoo_type', 'hide_empty' => true));
$features = get_terms(array('taxonomy' => 'features', 'hide_empty' => true));
?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">
        
        <!-- Hero Section -->
        <section class="zoo-hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-content-wrapper">
                <div class="hero-content">
                    <!-- Breadcrumbs -->
                    <nav class="zoo-breadcrumbs" aria-label="Breadcrumb">
                        <ol class="breadcrumb-list">
                            <li class="breadcrumb-item">
                                <a href="<?php echo home_url(); ?>">🏠 Home</a>
                            </li>
                            <?php if ($state_name) : ?>
                            <li class="breadcrumb-item">
                                <span class="breadcrumb-separator">›</span>
                                <a href="<?php echo get_state_page_url($state_name); ?>"><?php echo esc_html($state_name); ?></a>
                            </li>
                            <?php endif; ?>
                            <li class="breadcrumb-item current">
                                <span class="breadcrumb-separator">›</span>
                                <span><?php echo esc_html($city_name); ?></span>
                            </li>
                        </ol>
                    </nav>
                    
                    <!-- Hero Title -->
                    <h1 class="zoo-title"><?php the_title(); ?></h1>
                </div>
            </div>
        </section>

        <!-- Main Content Container -->
        <div class="container">
            
            <!-- Existing Page Content -->
            <section class="page-content-section">
                <?php
                while (have_posts()) :
                    the_post();
                    the_content();
                endwhile;
                ?>
            </section>

            <!-- All Petting Zoos Section -->
            <section class="all-zoos-section">
                <h2>All Petting Zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?></h2>
                <p class="section-intro">Find Your Perfect Petting Zoo Experience in <?php echo esc_html($city_name . ', ' . $state_name); ?></p>
                
                <!-- Filters -->
                <div class="zoo-filters-section">
                    <div class="zoo-filters">
                        <div class="filter-group">
                            <label for="animal-type-filter">🐾 Animal Types:</label>
                            <select id="animal-type-filter" name="animal_type">
                                <option value="">All Animals</option>
                                <?php foreach ($animal_types as $type) : ?>
                                    <option value="<?php echo esc_attr($type->slug); ?>"><?php echo esc_html($type->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="zoo-type-filter">🏛️ Zoo Types:</label>
                            <select id="zoo-type-filter" name="zoo_type">
                                <option value="">All Types</option>
                                <?php foreach ($zoo_types as $type) : ?>
                                    <option value="<?php echo esc_attr($type->slug); ?>"><?php echo esc_html($type->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="features-filter">⭐ Features:</label>
                            <select id="features-filter" name="features">
                                <option value="">All Features</option>
                                <?php foreach ($features as $feature) : ?>
                                    <option value="<?php echo esc_attr($feature->slug); ?>"><?php echo esc_html($feature->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <button type="button" class="clear-filters-btn" onclick="clearAllFilters()">Clear All Filters</button>
                    </div>
                </div>

                <!-- Google Maps -->
                <div class="zoo-map-container">
                    <div id="city-zoo-map" style="height: 400px; width: 100%; border-radius: 10px;"></div>
                </div>

                <!-- Results Info -->
                <div class="results-info">
                    <p id="results-count">Showing <?php echo count($all_zoos); ?> petting zoo<?php echo count($all_zoos) !== 1 ? 's' : ''; ?> in <?php echo esc_html($city_name . ', ' . $state_name); ?></p>
                </div>

                <!-- Petting Zoos Grid -->
                <div id="zoos-grid" class="petting-zoo-cards-grid">
                    <?php foreach ($all_zoos as $zoo) : 
                        $zoo_id = $zoo->ID;
                        $address = get_post_meta($zoo_id, '_petting_zoo_address', true);
                        $content = wp_trim_words(get_post_field('post_content', $zoo_id), 12);

                        // ### MODIFICATION START: Always use the placeholder image ###
                        // The logic to get the real image has been removed.
                        $image_url = '/wp-content/uploads/2025/06/placeholder.jpg';
                        // ### MODIFICATION END ###
                        
                        // Get taxonomies for filtering
                        $zoo_animal_types = wp_get_post_terms($zoo_id, 'animal_type', array('fields' => 'slugs'));
                        $zoo_zoo_types = wp_get_post_terms($zoo_id, 'zoo_type', array('fields' => 'slugs'));
                        $zoo_features = wp_get_post_terms($zoo_id, 'features', array('fields' => 'slugs'));
                        
                        $data_attributes = sprintf(
                            'data-animal-types="%s" data-zoo-types="%s" data-features="%s"',
                            esc_attr(implode(',', $zoo_animal_types)),
                            esc_attr(implode(',', $zoo_zoo_types)),
                            esc_attr(implode(',', $zoo_features))
                        );
                    ?>
                        <div class="petting-zoo-card" <?php echo $data_attributes; ?>>
                            <div class="card-image">
                                <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($zoo->post_title); ?>">
                            </div>
                            <div class="card-content">
                                <h3 class="zoo-name"><?php echo esc_html($zoo->post_title); ?></h3>
                                <?php if ($address) : ?>
                                    <p class="zoo-address">📍 <?php echo esc_html($address); ?></p>
                                <?php endif; ?>
                                <p class="zoo-description"><?php echo esc_html($content); ?></p>
                                <a href="<?php echo get_zoo_page_url($zoo); ?>" class="view-details-btn">View Details →</a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>

            <!-- Best Petting Zoos by Ranking -->
            <section class="featured-zoos-section">
                <h3>Best Petting Zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> by Ranking</h3>
                <p>Discover the highest-rated petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> based on visitor reviews and family experiences. These top-ranked destinations consistently deliver exceptional animal encounters and memorable family adventures in the area.</p>

                <div class="featured-zoos-grid">
                    <?php
                    // Get top 3 zoos by rating (you can implement rating logic here)
                    $top_zoos = array_slice($all_zoos, 0, 3);
                    console_log_city("Showing " . count($top_zoos) . " top zoos for ranking section");
                    foreach ($top_zoos as $zoo) :
                        render_zoo_card_city($zoo);
                    endforeach;
                    ?>
                </div>
            </section>

            <!-- Best Petting Zoos for Birthday Parties -->
            <section class="featured-zoos-section">
                <h3>Best Petting Zoos for Birthday Parties in <?php echo esc_html($city_name . ', ' . $state_name); ?></h3>
                <p>Celebrate your child's special day with these amazing petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> that offer birthday party packages and special event services. Perfect for birthday parties, school events, and community gatherings in the local area.</p>

                <div class="featured-zoos-grid">
                    <?php
                    // Get zoos with birthday party features
                    $party_zoos = array();
                    foreach ($all_zoos as $zoo) {
                        $zoo_features = wp_get_post_terms($zoo->ID, 'features', array('fields' => 'slugs'));
                        if (in_array('birthday-parties', $zoo_features) || in_array('party-packages', $zoo_features)) {
                            $party_zoos[] = $zoo;
                        }
                    }
                    $party_zoos = array_slice($party_zoos, 0, 3);
                    if (empty($party_zoos)) {
                        $party_zoos = array_slice($all_zoos, 0, 3); // Fallback to first 3
                    }
                    console_log_city("Showing " . count($party_zoos) . " party zoos for birthday section");
                    foreach ($party_zoos as $zoo) :
                        render_zoo_card_city($zoo);
                    endforeach;
                    ?>
                </div>
            </section>

            <!-- Best Mobile Petting Zoos -->
            <section class="featured-zoos-section">
                <h3>Best Mobile Petting Zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?></h3>
                <p>Bring the petting zoo experience directly to your location with these mobile petting zoo services in <?php echo esc_html($city_name . ', ' . $state_name); ?>. Perfect for birthday parties, school events, and community gatherings, these mobile services deliver hands-on animal experiences right to your doorstep.</p>

                <div class="featured-zoos-grid">
                    <?php
                    // Get mobile zoos
                    $mobile_zoos = array();
                    foreach ($all_zoos as $zoo) {
                        $zoo_types = wp_get_post_terms($zoo->ID, 'zoo_type', array('fields' => 'slugs'));
                        if (in_array('mobile', $zoo_types) || in_array('mobile-petting-zoo', $zoo_types)) {
                            $mobile_zoos[] = $zoo;
                        }
                    }
                    $mobile_zoos = array_slice($mobile_zoos, 0, 3);
                    if (empty($mobile_zoos)) {
                        $mobile_zoos = array_slice($all_zoos, 0, 3); // Fallback
                    }
                    console_log_city("Showing " . count($mobile_zoos) . " mobile zoos for mobile section");
                    foreach ($mobile_zoos as $zoo) :
                        render_zoo_card_city($zoo);
                    endforeach;
                    ?>
                </div>
            </section>

            <!-- Best Petting Zoos with Bunnies -->
            <section class="featured-zoos-section">
                <h3>Best Petting Zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> with Bunnies</h3>
                <p>Discover petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> that feature adorable bunnies and rabbits for gentle interactions. These family-friendly destinations offer special bunny encounters that are perfect for young children and create heartwarming memories with these soft, cuddly animals.</p>

                <div class="featured-zoos-grid">
                    <?php
                    // Get zoos with bunnies
                    $bunny_zoos = array();
                    foreach ($all_zoos as $zoo) {
                        $zoo_animals = wp_get_post_terms($zoo->ID, 'animal_type', array('fields' => 'slugs'));
                        if (in_array('bunnies', $zoo_animals) || in_array('rabbits', $zoo_animals)) {
                            $bunny_zoos[] = $zoo;
                        }
                    }
                    $bunny_zoos = array_slice($bunny_zoos, 0, 3);
                    if (empty($bunny_zoos)) {
                        $bunny_zoos = array_slice($all_zoos, 0, 3); // Fallback
                    }
                    console_log_city("Showing " . count($bunny_zoos) . " bunny zoos for bunnies section");
                    foreach ($bunny_zoos as $zoo) :
                        render_zoo_card_city($zoo);
                    endforeach;
                    ?>
                </div>
            </section>

            <!-- Best Indoor Petting Zoos -->
            <section class="featured-zoos-section">
                <h3>Best Indoor Petting Zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?></h3>
                <p>Enjoy year-round animal experiences at these indoor petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?>. Perfect for any weather, these climate-controlled facilities offer comfortable environments for families to interact with animals regardless of the season.</p>

                <div class="featured-zoos-grid">
                    <?php
                    // Get indoor zoos
                    $indoor_zoos = array();
                    foreach ($all_zoos as $zoo) {
                        $zoo_features = wp_get_post_terms($zoo->ID, 'features', array('fields' => 'slugs'));
                        if (in_array('indoor', $zoo_features) || in_array('climate-controlled', $zoo_features)) {
                            $indoor_zoos[] = $zoo;
                        }
                    }
                    $indoor_zoos = array_slice($indoor_zoos, 0, 3);
                    if (empty($indoor_zoos)) {
                        $indoor_zoos = array_slice($all_zoos, 0, 3); // Fallback
                    }
                    console_log_city("Showing " . count($indoor_zoos) . " indoor zoos for indoor section");
                    foreach ($indoor_zoos as $zoo) :
                        render_zoo_card_city($zoo);
                    endforeach;
                    ?>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="section faq-section">
                <h2 class="section-title">Frequently Asked Questions</h2>

                <div class="faq-item">
                    <div class="faq-question">What are the best petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?>?</div>
                    <div class="faq-answer">
                        <p>The best petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> offer safe, educational, and fun experiences for families. Look for facilities with well-cared-for animals, knowledgeable staff, and proper safety measures. Our directory features top-rated petting zoos in the <?php echo esc_html($city_name); ?> area.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Are petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> safe for children?</div>
                    <div class="faq-answer">
                        <p>Yes, reputable petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> prioritize safety with gentle animals, proper supervision, and clear safety guidelines. Always supervise young children and follow the facility's rules for interacting with animals.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">What should I bring to a petting zoo in <?php echo esc_html($city_name . ', ' . $state_name); ?>?</div>
                    <div class="faq-answer">
                        <p>Bring hand sanitizer, wear closed-toe shoes, and dress in clothes you don't mind getting dirty. Many petting zoos provide animal feed, but check ahead. Don't forget your camera to capture special moments with your family!</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">Do petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?> require reservations?</div>
                    <div class="faq-answer">
                        <p>While many petting zoos accept walk-ins, it's recommended to call ahead, especially for larger groups or during peak seasons. Some facilities in the <?php echo esc_html($city_name); ?> area require reservations for special programs or birthday parties.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">What's the best time to visit petting zoos in <?php echo esc_html($city_name . ', ' . $state_name); ?>?</div>
                    <div class="faq-answer">
                        <p>Animals are typically most active in the morning and late afternoon. Spring and fall offer comfortable weather for outdoor visits. Weekdays are usually less crowded than weekends in the <?php echo esc_html($city_name); ?> area.</p>
                    </div>
                </div>
            </section>

            <!-- Other Cities in the State Section -->
            <section class="other-cities-section">
                <h2>Other Cities in <?php echo esc_html($state_name); ?></h2>
                <p class="section-intro">Explore petting zoos in other cities across <?php echo esc_html($state_name); ?></p>

                <div class="city-cards-grid">
                    <?php
                    // Get all cities in the same state with petting zoo counts
                    $other_cities = array();

                    // Search by address patterns to find cities
                    $all_zoo_posts = get_posts(array(
                        'post_type' => 'petting_zoo',
                        'posts_per_page' => -1,
                        'post_status' => 'publish'
                    ));

                    $cities_found = array();

                    foreach ($all_zoo_posts as $zoo_post) {
                        $address = get_post_meta($zoo_post->ID, '_petting_zoo_address', true);
                        if ($address && strpos($address, $state_name) !== false) {
                            // Extract city from address (assuming format: "Street, City, State ZIP")
                            $address_parts = explode(',', $address);
                            if (count($address_parts) >= 2) {
                                $potential_city = trim($address_parts[count($address_parts) - 2]);
                                // Remove state abbreviations and ZIP codes
                                $potential_city = preg_replace('/\s+[A-Z]{2}\s*\d*$/', '', $potential_city);
                                $potential_city = trim($potential_city);

                                if ($potential_city && $potential_city !== $city_name && strlen($potential_city) > 2) {
                                    if (!isset($cities_found[$potential_city])) {
                                        $cities_found[$potential_city] = 0;
                                    }
                                    $cities_found[$potential_city]++;
                                }
                            }
                        }
                    }

                    // Sort cities by zoo count
                    arsort($cities_found);

                    // Display up to 12 other cities
                    $city_count = 0;
                    foreach ($cities_found as $other_city => $zoo_count) :
                        if ($city_count >= 12) break;
                        $city_count++;

                        // Create hierarchical URL: /state/city-abbr/
                        $city_url = get_hierarchical_city_url($other_city, $state_name);
                    ?>
                        <div class="city-card">
                            <div class="city-card-content">
                                <h3 class="city-name">
                                    <a href="<?php echo esc_url($city_url); ?>" class="city-name-link"><?php echo esc_html($other_city); ?></a>
                                </h3>
                                <p class="zoo-count"><?php echo $zoo_count; ?> petting zoo<?php echo $zoo_count !== 1 ? 's' : ''; ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </section>

        </div>
    </main>
</div>

<?php
// ### MODIFICATION START: Always use the placeholder image in the helper function ###
// Helper function to render zoo cards for city pages
function render_zoo_card_city($zoo) {
    $zoo_id = $zoo->ID;
    $address = get_post_meta($zoo_id, '_petting_zoo_address', true);
    $content = wp_trim_words(get_post_field('post_content', $zoo_id), 12);

    // The logic to get the real image has been removed. We always use the placeholder.
    $image_url = '/wp-content/uploads/2025/06/placeholder.jpg';
    ?>
    <div class="petting-zoo-card">
        <div class="card-image">
            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($zoo->post_title); ?>">
        </div>
        <div class="card-content">
            <h3 class="zoo-name"><?php echo esc_html($zoo->post_title); ?></h3>
            <?php if ($address) : ?>
                <p class="zoo-address">📍 <?php echo esc_html($address); ?></p>
            <?php endif; ?>
            <p class="zoo-description"><?php echo esc_html($content); ?></p>
            <a href="<?php echo get_zoo_page_url($zoo); ?>" class="view-details-btn">View Details →</a>
        </div>
    </div>
    <?php
}
// ### MODIFICATION END ###
?>


<style>
/* City Page Specific Styles - Inherits from State Page Styles */
.zoo-hero-section {
    background: #2e602f;
    color: white;
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    width: 100vw;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    margin-bottom: 3rem;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(46, 96, 47, 0.1);
}

.hero-content-wrapper {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.zoo-breadcrumbs {
    margin-bottom: 1rem;
}

.breadcrumb-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: rgba(255, 255, 255, 0.7);
}

.breadcrumb-item.current span {
    color: white;
    font-weight: 600;
}

.zoo-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    color: white;
}

/* Filter Styles */
.zoo-filters-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin: 2rem 0;
}

.zoo-filters {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: #2e602f;
    font-size: 0.9rem;
}

.filter-group select {
    padding: 0.75rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 1rem;
    min-width: 180px;
    transition: border-color 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #2e602f;
}

.clear-filters-btn {
    background: #2e602f;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    height: fit-content;
}

.clear-filters-btn:hover {
    background: #1e4020;
}

/* Zoo Cards Grid */


.petting-zoo-cards-grid {
    display: grid;
    /* THIS is the correct, modern way to do a responsive grid */
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); 
    gap: 2rem;
    margin: 3rem 0;
}

.featured-zoos-grid {
    display: grid;
    /* MAKE THIS LINE IDENTICAL TO THE ONE ABOVE */
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
    /* The justify-content line is no longer needed with this method, so we remove it. */
}


/* Zoo Card Styling */
.petting-zoo-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid #f0f0f0;
}

.petting-zoo-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 35px rgba(46, 96, 47, 0.15);
    border-color: #2e602f;
}

.card-image {
    position: relative;
    overflow: hidden;
    height: 220px;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.petting-zoo-card:hover .card-image img {
    transform: scale(1.05);
}

.card-content {
    padding: 1.5rem;
}

.zoo-name {
    color: #2e602f;
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-align: center;
}

.zoo-address {
    color: #666;
    font-size: 0.95rem;
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.zoo-description {
    color: #555;
    font-size: 0.95rem;
    line-height: 1.5;
    margin: 1rem 0;
}

.view-details-btn {
    background: #2e602f;
    color: white !important;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    transition: background-color 0.3s ease;
    width: 100%;
    text-align: center;
}

.view-details-btn:hover {
    background: #1e4020;
}

/* Featured Sections */
.featured-zoos-section {
    margin: 3rem 0;
}

.featured-zoos-section h3 {
    color: #2e602f;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.featured-zoos-section p {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* City Cards Grid */
.city-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.city-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid #f0f0f0;
}

.city-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(46, 96, 47, 0.15);
    border-color: #2e602f;
}

.city-card-content {
    padding: 1.5rem;
    text-align: center;
}

.city-name {
    color: #2e602f;
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.zoo-count {
    color: #666;
    font-size: 1rem;
    margin: 0.5rem 0 1rem 0;
}

.city-name-link {
    color: #2e602f;
    text-decoration: none;
    transition: color 0.3s ease;
}

.city-name-link:hover {
    color: #1e4020;
}

/* Map Container */
.zoo-map-container {
    margin: 2rem 0;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Results Info */
.results-info {
    margin: 1rem 0;
    text-align: center;
}

.results-info p {
    color: #666;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Section Spacing */
.page-content-section {
    margin: 2rem 0 3rem 0;
}

.all-zoos-section {
    margin: 3rem 0;
}

.all-zoos-section h2 {
    color: #2e602f;
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
}

.section-intro {
    color: #666;
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .zoo-hero-section {
        height: 250px;
    }

    .zoo-title {
        font-size: 2rem;
    }

    .zoo-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group select,
    .clear-filters-btn {
        width: 100%;
        min-width: auto;
    }

    .petting-zoo-cards-grid,
    .featured-zoos-grid {
        grid-template-columns: 1fr;
    }

    .city-cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}
</style>

<script>
console.log('City page JavaScript loaded');

// Global variables
let map;
let markers = [];
let allZoos = [];

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing city page');

    // Collect all zoo data
    collectZooData();

    // Initialize Google Maps
    initializeMap();

    // Setup filter event listeners
    setupFilters();

    // Setup FAQ toggles
    setupFAQs();
});

// Collect zoo data from the page
function collectZooData() {
    console.log('Collecting zoo data for city page');
    const zooCards = document.querySelectorAll('.petting-zoo-card');
    allZoos = [];

    zooCards.forEach(card => {
        const zooData = {
            element: card,
            name: card.querySelector('.zoo-name')?.textContent || '',
            address: card.querySelector('.zoo-address')?.textContent || '',
            animalTypes: card.getAttribute('data-animal-types')?.split(',') || [],
            zooTypes: card.getAttribute('data-zoo-types')?.split(',') || [],
            features: card.getAttribute('data-features')?.split(',') || []
        };
        allZoos.push(zooData);
    });

    console.log('Collected data for', allZoos.length, 'zoos in city');
}

// Initialize Google Maps
function initializeMap() {
    console.log('Initializing Google Maps for city page');

    const mapElement = document.getElementById('city-zoo-map');
    if (!mapElement) {
        console.log('City map element not found');
        return;
    }

    // Load Google Maps API if not already loaded
    if (typeof google === 'undefined') {
        console.log('Loading Google Maps API for city page');
        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyDSY9K8KUaLvpm42ubwTCU3jJKtzZ8GXmA&loading=async&callback=createCityMap';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    } else {
        createCityMap();
    }
}

// Create the map for city
function createCityMap() {
    console.log('Creating city map');

    const mapElement = document.getElementById('city-zoo-map');
    if (!mapElement) return;

    // Default center (you can adjust based on city)
    const center = { lat: 39.8283, lng: -98.5795 }; // Center of US

    map = new google.maps.Map(mapElement, {
        center: center,
        zoom: 10, // Higher zoom for city level
        mapTypeId: google.maps.MapTypeId.ROADMAP
    });

    // Add markers for all zoos (you would need to add lat/lng data to zoo cards)
    addCityMapMarkers();
}

// Add markers to city map
function addCityMapMarkers() {
    console.log('Adding city map markers');
    // This would require lat/lng data for each zoo
    // For now, just log that markers would be added
    console.log('City map markers would be added here with zoo coordinates');
}

// Setup filter functionality
function setupFilters() {
    console.log('Setting up city page filters');

    const animalFilter = document.getElementById('animal-type-filter');
    const zooFilter = document.getElementById('zoo-type-filter');
    const featuresFilter = document.getElementById('features-filter');

    if (animalFilter) {
        animalFilter.addEventListener('change', applyFilters);
    }
    if (zooFilter) {
        zooFilter.addEventListener('change', applyFilters);
    }
    if (featuresFilter) {
        featuresFilter.addEventListener('change', applyFilters);
    }
}

// Apply filters
function applyFilters() {
    console.log('Applying city page filters');

    const animalType = document.getElementById('animal-type-filter')?.value || '';
    const zooType = document.getElementById('zoo-type-filter')?.value || '';
    const features = document.getElementById('features-filter')?.value || '';

    console.log('City filters:', { animalType, zooType, features });

    let visibleCount = 0;

    allZoos.forEach(zoo => {
        let show = true;

        // Check animal type filter
        if (animalType && !zoo.animalTypes.includes(animalType)) {
            show = false;
        }

        // Check zoo type filter
        if (zooType && !zoo.zooTypes.includes(zooType)) {
            show = false;
        }

        // Check features filter
        if (features && !zoo.features.includes(features)) {
            show = false;
        }

        // Show/hide the zoo card
        if (show) {
            zoo.element.style.display = 'block';
            visibleCount++;
        } else {
            zoo.element.style.display = 'none';
        }
    });

    // Update results count
    updateResultsCount(visibleCount);

    console.log('City page showing', visibleCount, 'of', allZoos.length, 'zoos');
}

// Clear all filters
function clearAllFilters() {
    console.log('Clearing all city page filters');

    document.getElementById('animal-type-filter').value = '';
    document.getElementById('zoo-type-filter').value = '';
    document.getElementById('features-filter').value = '';

    // Show all zoos
    allZoos.forEach(zoo => {
        zoo.element.style.display = 'block';
    });

    updateResultsCount(allZoos.length);
}

// Update results count
function updateResultsCount(count) {
    const resultsElement = document.getElementById('results-count');
    if (resultsElement) {
        const cityName = '<?php echo esc_js($city_name); ?>';
        const stateName = '<?php echo esc_js($state_name); ?>';
        const plural = count !== 1 ? 's' : '';
        resultsElement.textContent = `Showing ${count} petting zoo${plural} in ${cityName}, ${stateName}`;
    }
}

// Setup FAQ toggles
function setupFAQs() {
    console.log('Setting up city page FAQ toggles');

    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');

        if (question && answer) {
            // Initially hide answers
            answer.style.display = 'none';

            // Add click handler
            question.addEventListener('click', function() {
                const isVisible = answer.style.display === 'block';

                // Hide all other answers
                faqItems.forEach(otherItem => {
                    const otherAnswer = otherItem.querySelector('.faq-answer');
                    if (otherAnswer) {
                        otherAnswer.style.display = 'none';
                        otherItem.classList.remove('active');
                    }
                });

                // Toggle current answer
                if (!isVisible) {
                    answer.style.display = 'block';
                    item.classList.add('active');
                }
            });

            // Add cursor pointer
            question.style.cursor = 'pointer';
        }
    });
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('City page error:', e.error);
});

console.log('City page script setup complete');
</script>

<?php get_footer(); ?>
